/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'images.unsplash.com',
      'source.unsplash.com',
      'images.pexels.com',
      'images.pixabay.com',
      'media.istockphoto.com',
      'img.freepik.com',
      'cdn.pixabay.com',
      'cdn.pexels.com',
      'cdn.unsplash.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: '**.pexels.com',
      },
      {
        protocol: 'https',
        hostname: '**.pixabay.com',
      },
      {
        protocol: 'https',
        hostname: '**.istockphoto.com',
      },
      {
        protocol: 'https',
        hostname: '**.freepik.com',
      }
    ],
  },
}

module.exports = nextConfig 