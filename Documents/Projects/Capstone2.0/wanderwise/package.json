{"name": "wanderwise", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.17.0", "@radix-ui/react-progress": "^1.1.7", "@tailwindcss/vite": "^4.1.8", "amadeus": "^11.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "lucide-react": "^0.507.0", "next": "15.3.1", "next-themes": "^0.4.6", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "vite": "^6.3.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.8", "@types/node": "22.14.1", "@types/react": "19.1.2", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4.1.8", "typescript": "5.8.3"}}